# Ultimate Electrical Designer - Frontend Documentation

## Overview

The Ultimate Electrical Designer frontend is built with Next.js 15.3.0, TypeScript, React Query, and Zustand. It follows a component-based architecture with strict type safety and comprehensive testing.

## Architecture

### Tech Stack
- **Framework:** Next.js 15.3.0 with App Router
- **Language:** TypeScript 5.8+
- **Styling:** Tailwind CSS 4.1+
- **State Management:**
  - Server State: React Query
  - Client State: Zustand
- **Testing:**
  - Unit Testing: Vitest + React Testing Library
  - E2E Testing: Playwright
- **Code Quality:**
  - ESLint
  - Prettier
  - TypeScript strict mode

### Directory Structure

```
client/
├── public/              # Static assets
├── src/
│   ├── app/             # Next.js App Router pages
│   │   ├── (auth)/      # Authentication routes
│   │   ├── admin/       # Admin routes
│   │   ├── dashboard/   # Dashboard routes
│   │   ├── profile/     # User profile routes
│   │   └── layout.tsx   # Root layout
│   ├── components/      # Reusable components
│   │   ├── admin/       # Admin components
│   │   ├── auth/        # Authentication components
│   │   ├── layout/      # Layout components
│   │   └── ui/          # UI components
│   ├── hooks/           # Custom React hooks
│   │   ├── api/         # API hooks
│   │   └── __tests__/   # Hook tests
│   ├── services/        # API services
│   ├── stores/          # Zustand stores
│   ├── styles/          # Global styles
│   ├── test/            # Test utilities
│   │   ├── e2e/         # E2E tests
│   │   └── utils.tsx    # Test helpers
│   └── types/           # TypeScript types
├── tailwind.config.js   # Tailwind configuration
├── tsconfig.json        # TypeScript configuration
└── vitest.config.ts     # Vitest configuration
```

## Key Features

### 1. Landing Page
- Responsive design with mobile-first approach
- Dynamic content based on authentication state
- Feature showcase with interactive elements
- Professional UI with proper typography and spacing

### 2. Authentication System
- JWT-based authentication with automatic refresh
- Role-based access control (Admin, Editor, Viewer)
- Protected routes with automatic redirects
- Persistent sessions across browser sessions

### 3. Admin Dashboard
- User management with CRUD operations
- Statistics dashboard with real-time metrics
- Permission controls with proper validation
- Responsive tables for mobile viewing

### 4. User Profile
- User information display and editing
- Password change functionality
- Account settings management
- Activity history tracking

## Component Documentation

### UI Components

#### Button
```tsx
<Button 
  variant="primary" | "secondary" | "outline" | "ghost" | "link" | "destructive"
  size="sm" | "md" | "lg" | "icon"
  onClick={handleClick}
  disabled={false}
  className="custom-class"
>
  Button Text
</Button>
```

#### Form Components
- `Input`: Text input with validation
- `Select`: Dropdown selection
- `Checkbox`: Boolean selection
- `RadioGroup`: Option selection
- `Switch`: Toggle component

### Authentication Components

#### LoginForm
```tsx
<LoginForm 
  onSuccess={handleSuccess} 
  redirectTo="/dashboard" 
/>
```

#### UserProfile
```tsx
<UserProfile 
  editable={true} 
  showSettings={true} 
/>
```

### Admin Components

#### UserManagement
```tsx
<UserManagement 
  pageSize={10} 
  showFilters={true} 
/>
```

## State Management

### Server State (React Query)
- API data fetching and caching
- Automatic refetching and invalidation
- Optimistic updates
- Error handling

### Client State (Zustand)
- Authentication state
- UI preferences
- Form state
- Application settings

## Hooks

### useAuth
```tsx
const { 
  user,
  token,
  isAuthenticated,
  isLoading,
  login,
  logout,
  hasRole,
  isAdmin,
  loginError,
  logoutError
} = useAuth();
```

### useApi
```tsx
const { 
  data,
  isLoading,
  error,
  refetch
} = useApi(endpoint, options);
```

## Testing

### Unit Testing
- Component tests with React Testing Library
- Hook tests with renderHook
- Store tests with state assertions

### E2E Testing
- User flows with Playwright
- Cross-browser testing
- Mobile responsive testing
- Accessibility testing

## API Integration

### Endpoints
- `/api/v1/auth/login`: Authentication
- `/api/v1/auth/logout`: Logout
- `/api/v1/users/*`: User management
- `/api/v1/admin/*`: Admin operations
- `/api/v1/health`: System health check

### Error Handling
- Network error handling
- API error responses
- Validation errors
- Authentication errors

## Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
- `NEXT_PUBLIC_API_URL`: API base URL
- `NEXT_PUBLIC_APP_ENV`: Environment (dev/staging/prod)
- `NEXT_PUBLIC_AUTH_STORAGE_KEY`: Auth storage key

## Contributing

### Development Workflow
1. Create feature branch
2. Implement changes with tests
3. Run verification checks
4. Submit pull request
5. Address review feedback

### Code Standards
- Follow TypeScript strict mode
- Maintain 100% test coverage
- Use proper component structure
- Follow naming conventions

## Troubleshooting

### Common Issues
- Authentication token expiration
- API connection errors
- Test environment setup
- Build configuration

### Debugging
- React DevTools
- Network request monitoring
- Test debugging with Vitest
- E2E test debugging with Playwright Inspector
