[{"D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login\\page.tsx": "1", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\admin\\users\\page.tsx": "2", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard\\page.tsx": "3", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\layout.tsx": "4", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\page.tsx": "5", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\profile\\page.tsx": "6", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\AdminDashboard.tsx": "7", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\UserManagement.tsx": "8", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\LoginForm.tsx": "9", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\RouteGuard.tsx": "10", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\UserProfile.tsx": "11", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__\\LoginForm.test.tsx": "12", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Footer.tsx": "13", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Header.tsx": "14", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\layout\\DashboardLayout.tsx": "15", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Breadcrumbs.tsx": "16", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Sidebar.tsx": "17", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\button.tsx": "18", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\__tests__\\button.test.tsx": "19", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useAuth.ts": "20", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useUsers.ts": "21", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\useAuth.ts": "22", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\__tests__\\useAuth.test.tsx": "23", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\client.ts": "24", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth\\tokenManager.ts": "25", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\react-query.tsx": "26", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\utils.ts": "27", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\stores\\authStore.ts": "28", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e\\auth-flow.spec.ts": "29", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e\\landing-page.spec.ts": "30", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\setup.ts": "31", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\utils.tsx": "32", "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types\\api.ts": "33"}, {"size": 6786, "mtime": 1752566272013, "results": "34", "hashOfConfig": "35"}, {"size": 444, "mtime": 1752533984779, "results": "36", "hashOfConfig": "35"}, {"size": 7102, "mtime": 1752566987279, "results": "37", "hashOfConfig": "35"}, {"size": 716, "mtime": 1752533337301, "results": "38", "hashOfConfig": "35"}, {"size": 9341, "mtime": 1752533540977, "results": "39", "hashOfConfig": "35"}, {"size": 408, "mtime": 1752533977867, "results": "40", "hashOfConfig": "35"}, {"size": 8789, "mtime": 1752567052953, "results": "41", "hashOfConfig": "35"}, {"size": 14263, "mtime": 1752565671105, "results": "42", "hashOfConfig": "35"}, {"size": 4593, "mtime": 1752533613384, "results": "43", "hashOfConfig": "35"}, {"size": 2880, "mtime": 1752533798718, "results": "44", "hashOfConfig": "35"}, {"size": 12151, "mtime": 1752533651942, "results": "45", "hashOfConfig": "35"}, {"size": 4447, "mtime": 1752565487396, "results": "46", "hashOfConfig": "35"}, {"size": 1969, "mtime": 1752533460904, "results": "47", "hashOfConfig": "35"}, {"size": 4783, "mtime": 1752533448784, "results": "48", "hashOfConfig": "35"}, {"size": 2146, "mtime": 1752533862788, "results": "49", "hashOfConfig": "35"}, {"size": 3034, "mtime": 1752533815935, "results": "50", "hashOfConfig": "35"}, {"size": 6871, "mtime": 1752533848515, "results": "51", "hashOfConfig": "35"}, {"size": 2011, "mtime": 1752533424141, "results": "52", "hashOfConfig": "35"}, {"size": 2480, "mtime": 1752534054158, "results": "53", "hashOfConfig": "35"}, {"size": 3442, "mtime": 1752533309928, "results": "54", "hashOfConfig": "35"}, {"size": 3399, "mtime": 1752533326689, "results": "55", "hashOfConfig": "35"}, {"size": 3868, "mtime": 1752533404748, "results": "56", "hashOfConfig": "35"}, {"size": 4165, "mtime": 1752567221387, "results": "57", "hashOfConfig": "35"}, {"size": 7237, "mtime": 1752533283312, "results": "58", "hashOfConfig": "35"}, {"size": 4666, "mtime": 1752533384996, "results": "59", "hashOfConfig": "35"}, {"size": 1515, "mtime": 1752533293336, "results": "60", "hashOfConfig": "35"}, {"size": 166, "mtime": 1752533430285, "results": "61", "hashOfConfig": "35"}, {"size": 3106, "mtime": 1752533364141, "results": "62", "hashOfConfig": "35"}, {"size": 5847, "mtime": 1752534151955, "results": "63", "hashOfConfig": "35"}, {"size": 3492, "mtime": 1752534127532, "results": "64", "hashOfConfig": "35"}, {"size": 1306, "mtime": 1752534878581, "results": "65", "hashOfConfig": "35"}, {"size": 2432, "mtime": 1752567204646, "results": "66", "hashOfConfig": "35"}, {"size": 3894, "mtime": 1752533254494, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "y7mmad", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\(auth)\\login\\page.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\admin\\users\\page.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\dashboard\\page.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\layout.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\page.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\app\\profile\\page.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\AdminDashboard.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\admin\\UserManagement.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\LoginForm.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\RouteGuard.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\UserProfile.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\auth\\__tests__\\LoginForm.test.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Footer.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\common\\Header.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\layout\\DashboardLayout.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Breadcrumbs.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\navigation\\Sidebar.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\button.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\components\\ui\\__tests__\\button.test.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useAuth.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\api\\useUsers.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\useAuth.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\hooks\\__tests__\\useAuth.test.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\api\\client.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\auth\\tokenManager.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\react-query.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\lib\\utils.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\stores\\authStore.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e\\auth-flow.spec.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\e2e\\landing-page.spec.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\setup.ts", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\test\\utils.tsx", [], [], "D:\\Projects\\ultimate-electrical-designer\\client\\src\\types\\api.ts", [], []]