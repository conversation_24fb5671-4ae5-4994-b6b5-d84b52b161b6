{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 1}], "isBlockCoverage": true}, {"functionName": "useRouter", "ranges": [{"startOffset": 349, "endOffset": 365, "count": 15}], "isBlockCoverage": true}, {"functionName": "usePathname", "ranges": [{"startOffset": 382, "endOffset": 391, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSearchParams", "ranges": [{"startOffset": 412, "endOffset": 439, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/__tests__/useAuth.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 15502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 15502, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 330, "endOffset": 485, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 523, "endOffset": 641, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1264, "endOffset": 1402, "count": 1}], "isBlockCoverage": true}, {"functionName": "createWrapper", "ranges": [{"startOffset": 1443, "endOffset": 1918, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1617, "endOffset": 1915, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1945, "endOffset": 5484, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1966, "endOffset": 2058, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2100, "endOffset": 2385, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2158, "endOffset": 2189, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2421, "endOffset": 3023, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2657, "endOffset": 2688, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2761, "endOffset": 2895, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3056, "endOffset": 3731, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3316, "endOffset": 3347, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3420, "endOffset": 3625, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3757, "endOffset": 4510, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4028, "endOffset": 4136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4189, "endOffset": 4220, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4348, "endOffset": 4404, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4549, "endOffset": 4983, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4581, "endOffset": 4689, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4742, "endOffset": 4773, "count": 2}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5022, "endOffset": 5480, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5152, "endOffset": 5245, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5298, "endOffset": 5329, "count": 2}], "isBlockCoverage": true}]}, {"scriptId": "1941", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/stores/authStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.persist.name", "ranges": [{"startOffset": 569, "endOffset": 1881, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuth", "ranges": [{"startOffset": 771, "endOffset": 916, "count": 5}], "isBlockCoverage": true}, {"functionName": "updateUser", "ranges": [{"startOffset": 984, "endOffset": 1077, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuth", "ranges": [{"startOffset": 1141, "endOffset": 1288, "count": 10}], "isBlockCoverage": true}, {"functionName": "setLoading", "ranges": [{"startOffset": 1335, "endOffset": 1445, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1362, "endOffset": 1435, "count": 6}], "isBlockCoverage": true}, {"functionName": "initializeAuth", "ranges": [{"startOffset": 1524, "endOffset": 1874, "count": 7}, {"startOffset": 1585, "endOffset": 1598, "count": 3}, {"startOffset": 1600, "endOffset": 1705, "count": 3}, {"startOffset": 1705, "endOffset": 1866, "count": 4}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2014, "endOffset": 2032, "count": 1}], "isBlockCoverage": true}, {"functionName": "partialize", "ranges": [{"startOffset": 2110, "endOffset": 2185, "count": 28}], "isBlockCoverage": true}, {"functionName": "onRehydrateStorage", "ranges": [{"startOffset": 2254, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2459, "endOffset": 2487, "count": 69}], "isBlockCoverage": true}, {"functionName": "useAuthUser", "ranges": [{"startOffset": 2511, "endOffset": 2552, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2653, "endOffset": 2680, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthToken", "ranges": [{"startOffset": 2705, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2849, "endOffset": 2877, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsAuthenticated", "ranges": [{"startOffset": 2908, "endOffset": 2960, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3068, "endOffset": 3102, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsLoading", "ranges": [{"startOffset": 3127, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3275, "endOffset": 3303, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthToken", "ranges": [{"startOffset": 3328, "endOffset": 3363, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3465, "endOffset": 3493, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthUser", "ranges": [{"startOffset": 3517, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3652, "endOffset": 3679, "count": 0}], "isBlockCoverage": false}, {"functionName": "isUserAuthenticated", "ranges": [{"startOffset": 3711, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3865, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1951", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 663, "endOffset": 801, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 852, "endOffset": 1024, "count": 0}], "isBlockCoverage": false}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 1026, "endOffset": 1602, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1710, "endOffset": 1745, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2108, "endOffset": 2132, "count": 4}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2386, "endOffset": 2415, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2646, "endOffset": 2679, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2905, "endOffset": 2933, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetch", "ranges": [{"startOffset": 2955, "endOffset": 3122, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3221, "endOffset": 3246, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetchError", "ranges": [{"startOffset": 3273, "endOffset": 3410, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3514, "endOffset": 3544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3937, "endOffset": 3978, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4231, "endOffset": 4276, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2144", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "useAuth", "ranges": [{"startOffset": 846, "endOffset": 3842, "count": 15}, {"startOffset": 3474, "endOffset": 3500, "count": 9}, {"startOffset": 3501, "endOffset": 3528, "count": 9}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1361, "endOffset": 1453, "count": 6}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1508, "endOffset": 1720, "count": 8}, {"startOffset": 1536, "endOffset": 1554, "count": 1}, {"startOffset": 1556, "endOffset": 1716, "count": 1}, {"startOffset": 1622, "endOffset": 1710, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1795, "endOffset": 2004, "count": 8}, {"startOffset": 1821, "endOffset": 1839, "count": 0}, {"startOffset": 1841, "endOffset": 2000, "count": 0}], "isBlockCoverage": true}, {"functionName": "login", "ranges": [{"startOffset": 2064, "endOffset": 2512, "count": 2}, {"startOffset": 2189, "endOffset": 2467, "count": 1}, {"startOffset": 2467, "endOffset": 2468, "count": 0}], "isBlockCoverage": true}, {"functionName": "logout", "ranges": [{"startOffset": 2531, "endOffset": 2869, "count": 1}, {"startOffset": 2625, "endOffset": 2720, "count": 0}], "isBlockCoverage": true}, {"functionName": "hasRole", "ranges": [{"startOffset": 2889, "endOffset": 2936, "count": 3}], "isBlockCoverage": true}, {"functionName": "isAdmin", "ranges": [{"startOffset": 2956, "endOffset": 3029, "count": 2}, {"startOffset": 2999, "endOffset": 3024, "count": 1}], "isBlockCoverage": true}, {"functionName": "requireAuth", "ranges": [{"startOffset": 3053, "endOffset": 3178, "count": 0}], "isBlockCoverage": false}, {"functionName": "requireAdmin", "ranges": [{"startOffset": 3203, "endOffset": 3364, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3938, "endOffset": 3961, "count": 15}], "isBlockCoverage": true}]}, {"scriptId": "2145", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/api/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "useLogin", "ranges": [{"startOffset": 747, "endOffset": 1601, "count": 15}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 1022, "endOffset": 1156, "count": 2}, {"startOffset": 1127, "endOffset": 1155, "count": 1}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 1173, "endOffset": 1490, "count": 1}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 1505, "endOffset": 1593, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1698, "endOffset": 1722, "count": 15}], "isBlockCoverage": true}, {"functionName": "useLogout", "ranges": [{"startOffset": 1726, "endOffset": 2377, "count": 15}], "isBlockCoverage": true}, {"functionName": "mutationFn", "ranges": [{"startOffset": 1994, "endOffset": 2107, "count": 1}], "isBlockCoverage": true}, {"functionName": "onSuccess", "ranges": [{"startOffset": 2124, "endOffset": 2239, "count": 1}], "isBlockCoverage": true}, {"functionName": "onError", "ranges": [{"startOffset": 2254, "endOffset": 2369, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2475, "endOffset": 2500, "count": 15}], "isBlockCoverage": true}, {"functionName": "useChangePassword", "ranges": [{"startOffset": 2504, "endOffset": 2801, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2907, "endOffset": 2940, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCurrentUser", "ranges": [{"startOffset": 2944, "endOffset": 3406, "count": 15}], "isBlockCoverage": true}, {"functionName": "queryFn", "ranges": [{"startOffset": 3154, "endOffset": 3275, "count": 3}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3509, "endOffset": 3539, "count": 15}], "isBlockCoverage": true}, {"functionName": "useUpdateProfile", "ranges": [{"startOffset": 3543, "endOffset": 4197, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4302, "endOffset": 4334, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2146", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/types/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 470, "endOffset": 494, "count": 0}], "isBlockCoverage": false}, {"functionName": "usersList", "ranges": [{"startOffset": 551, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "user", "ranges": [{"startOffset": 598, "endOffset": 619, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 812, "endOffset": 837, "count": 17}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1220, "endOffset": 1248, "count": 30}], "isBlockCoverage": true}]}]}