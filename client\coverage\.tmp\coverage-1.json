{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/auth/__tests__/tokenManager.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 16325, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 16325, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 345, "endOffset": 430, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 758, "endOffset": 5243, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 779, "endOffset": 866, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 881, "endOffset": 918, "count": 15}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 950, "endOffset": 1198, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1003, "endOffset": 1192, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1230, "endOffset": 1801, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1288, "endOffset": 1571, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1627, "endOffset": 1795, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1830, "endOffset": 2117, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1887, "endOffset": 2111, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2149, "endOffset": 2614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2204, "endOffset": 2380, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2439, "endOffset": 2608, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2646, "endOffset": 3822, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2700, "endOffset": 2998, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3048, "endOffset": 3341, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3393, "endOffset": 3522, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3582, "endOffset": 3816, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3851, "endOffset": 4344, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3908, "endOffset": 4162, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4212, "endOffset": 4338, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4380, "endOffset": 5239, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4444, "endOffset": 4752, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4802, "endOffset": 4935, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4995, "endOffset": 5233, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1937", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/auth/tokenManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 443, "endOffset": 4601, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAccessToken", "ranges": [{"startOffset": 639, "endOffset": 828, "count": 1}], "isBlockCoverage": true}, {"functionName": "getAccessToken", "ranges": [{"startOffset": 890, "endOffset": 1034, "count": 4}, {"startOffset": 1013, "endOffset": 1033, "count": 0}], "isBlockCoverage": true}, {"functionName": "setRefreshToken", "ranges": [{"startOffset": 1095, "endOffset": 1229, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRefreshToken", "ranges": [{"startOffset": 1292, "endOffset": 1438, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearTokens", "ranges": [{"startOffset": 1515, "endOffset": 1744, "count": 1}], "isBlockCoverage": true}, {"functionName": "hasAccessToken", "ranges": [{"startOffset": 1800, "endOffset": 1858, "count": 2}], "isBlockCoverage": true}, {"functionName": "hasRefreshToken", "ranges": [{"startOffset": 1915, "endOffset": 1975, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeToken", "ranges": [{"startOffset": 2123, "endOffset": 2571, "count": 9}, {"startOffset": 2474, "endOffset": 2567, "count": 3}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2350, "endOffset": 2410, "count": 138}], "isBlockCoverage": true}, {"functionName": "isTokenExpired", "ranges": [{"startOffset": 2649, "endOffset": 3007, "count": 4}, {"startOffset": 2749, "endOffset": 2764, "count": 3}, {"startOffset": 2766, "endOffset": 2899, "count": 2}, {"startOffset": 2899, "endOffset": 3003, "count": 0}], "isBlockCoverage": true}, {"functionName": "getTokenExpiration", "ranges": [{"startOffset": 3060, "endOffset": 3367, "count": 3}, {"startOffset": 3164, "endOffset": 3179, "count": 2}, {"startOffset": 3181, "endOffset": 3211, "count": 2}, {"startOffset": 3211, "endOffset": 3260, "count": 1}, {"startOffset": 3260, "endOffset": 3363, "count": 0}], "isBlockCoverage": true}, {"functionName": "initializeTokens", "ranges": [{"startOffset": 3427, "endOffset": 3733, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshTokenIfNeeded", "ranges": [{"startOffset": 3861, "endOffset": 4599, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4702, "endOffset": 4730, "count": 15}], "isBlockCoverage": true}]}, {"scriptId": "1938", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/stores/authStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.persist.name", "ranges": [{"startOffset": 569, "endOffset": 1881, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuth", "ranges": [{"startOffset": 771, "endOffset": 916, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 984, "endOffset": 1077, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuth", "ranges": [{"startOffset": 1141, "endOffset": 1288, "count": 0}], "isBlockCoverage": false}, {"functionName": "setLoading", "ranges": [{"startOffset": 1335, "endOffset": 1445, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeAuth", "ranges": [{"startOffset": 1524, "endOffset": 1874, "count": 1}, {"startOffset": 1585, "endOffset": 1598, "count": 0}, {"startOffset": 1600, "endOffset": 1705, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2014, "endOffset": 2032, "count": 1}], "isBlockCoverage": true}, {"functionName": "partialize", "ranges": [{"startOffset": 2110, "endOffset": 2185, "count": 1}], "isBlockCoverage": true}, {"functionName": "onRehydrateStorage", "ranges": [{"startOffset": 2254, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2459, "endOffset": 2487, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthUser", "ranges": [{"startOffset": 2511, "endOffset": 2552, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2653, "endOffset": 2680, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthToken", "ranges": [{"startOffset": 2705, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2849, "endOffset": 2877, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsAuthenticated", "ranges": [{"startOffset": 2908, "endOffset": 2960, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3068, "endOffset": 3102, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsLoading", "ranges": [{"startOffset": 3127, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3275, "endOffset": 3303, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthToken", "ranges": [{"startOffset": 3328, "endOffset": 3363, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3465, "endOffset": 3493, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthUser", "ranges": [{"startOffset": 3517, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3652, "endOffset": 3679, "count": 0}], "isBlockCoverage": false}, {"functionName": "isUserAuthenticated", "ranges": [{"startOffset": 3711, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3865, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}]}]}