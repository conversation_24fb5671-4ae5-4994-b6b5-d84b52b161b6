{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/api/__tests__/client.test.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 28878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 28878, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 594, "endOffset": 10136, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 637, "endOffset": 729, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 766, "endOffset": 825, "count": 13}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 877, "endOffset": 2363, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 972, "endOffset": 1587, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 1145, "endOffset": 1184, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1686, "endOffset": 2357, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 1888, "endOffset": 1927, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2415, "endOffset": 3755, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2503, "endOffset": 3262, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 2659, "endOffset": 2698, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3330, "endOffset": 3749, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 3519, "endOffset": 3551, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3808, "endOffset": 5267, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3897, "endOffset": 4763, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 4100, "endOffset": 4139, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4832, "endOffset": 5261, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 5026, "endOffset": 5058, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5319, "endOffset": 6284, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5407, "endOffset": 6278, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 5613, "endOffset": 5652, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6339, "endOffset": 7206, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6430, "endOffset": 7200, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 6587, "endOffset": 6626, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7268, "endOffset": 9268, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7334, "endOffset": 8059, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 7579, "endOffset": 7614, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8121, "endOffset": 8643, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 8207, "endOffset": 8247, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 8702, "endOffset": 9262, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 8840, "endOffset": 8871, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9322, "endOffset": 10132, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9391, "endOffset": 9693, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9762, "endOffset": 10126, "count": 1}], "isBlockCoverage": true}, {"functionName": "json", "ranges": [{"startOffset": 9848, "endOffset": 9895, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1937", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/api/client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "ApiClient", "ranges": [{"startOffset": 205, "endOffset": 351, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuthToken", "ranges": [{"startOffset": 420, "endOffset": 473, "count": 16}], "isBlockCoverage": true}, {"functionName": "clearAuthToken", "ranges": [{"startOffset": 520, "endOffset": 567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultHeaders", "ranges": [{"startOffset": 620, "endOffset": 825, "count": 13}, {"startOffset": 736, "endOffset": 801, "count": 1}], "isBlockCoverage": true}, {"functionName": "request", "ranges": [{"startOffset": 883, "endOffset": 2012, "count": 13}, {"startOffset": 1416, "endOffset": 1489, "count": 12}, {"startOffset": 1489, "endOffset": 1514, "count": 11}, {"startOffset": 1514, "endOffset": 1608, "count": 2}, {"startOffset": 1608, "endOffset": 1685, "count": 9}, {"startOffset": 1685, "endOffset": 2008, "count": 2}, {"startOffset": 1889, "endOffset": 2008, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1217, "endOffset": 1241, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2044, "endOffset": 2144, "count": 7}], "isBlockCoverage": true}, {"functionName": "post", "ranges": [{"startOffset": 2177, "endOffset": 2351, "count": 4}, {"startOffset": 2290, "endOffset": 2312, "count": 3}, {"startOffset": 2313, "endOffset": 2321, "count": 1}], "isBlockCoverage": true}, {"functionName": "put", "ranges": [{"startOffset": 2383, "endOffset": 2555, "count": 1}, {"startOffset": 2517, "endOffset": 2525, "count": 0}], "isBlockCoverage": true}, {"functionName": "delete", "ranges": [{"startOffset": 2590, "endOffset": 2696, "count": 1}], "isBlockCoverage": true}, {"functionName": "login", "ranges": [{"startOffset": 2729, "endOffset": 2936, "count": 1}, {"startOffset": 2853, "endOffset": 2906, "count": 0}], "isBlockCoverage": true}, {"functionName": "logout", "ranges": [{"startOffset": 2939, "endOffset": 3124, "count": 1}, {"startOffset": 3041, "endOffset": 3094, "count": 0}], "isBlockCoverage": true}, {"functionName": "changePassword", "ranges": [{"startOffset": 3127, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentUser", "ranges": [{"startOffset": 3362, "endOffset": 3551, "count": 1}, {"startOffset": 3468, "endOffset": 3521, "count": 0}], "isBlockCoverage": true}, {"functionName": "updateCurrentUser", "ranges": [{"startOffset": 3554, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsers", "ranges": [{"startOffset": 3759, "endOffset": 4457, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsersSummary", "ranges": [{"startOffset": 4460, "endOffset": 4724, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUser", "ranges": [{"startOffset": 4727, "endOffset": 4914, "count": 0}], "isBlockCoverage": false}, {"functionName": "createUser", "ranges": [{"startOffset": 4917, "endOffset": 5110, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 5113, "endOffset": 5315, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteUser", "ranges": [{"startOffset": 5318, "endOffset": 5485, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 5515, "endOffset": 5699, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5799, "endOffset": 5824, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6033, "endOffset": 6058, "count": 29}], "isBlockCoverage": true}]}]}