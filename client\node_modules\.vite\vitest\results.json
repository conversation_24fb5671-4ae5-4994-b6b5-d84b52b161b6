{"version": "1.6.1", "results": [[":src/lib/api/__tests__/client.test.ts", {"duration": 15, "failed": false}], [":src/lib/auth/__tests__/tokenManager.test.ts", {"duration": 15, "failed": false}], [":src/components/auth/__tests__/RouteGuard.test.tsx", {"duration": 93, "failed": true}], [":src/hooks/__tests__/useAuth.test.tsx", {"duration": 48, "failed": false}], [":src/components/ui/__tests__/button.test.tsx", {"duration": 477, "failed": false}], [":src/components/auth/__tests__/LoginForm.test.tsx", {"duration": 1795, "failed": false}]]}