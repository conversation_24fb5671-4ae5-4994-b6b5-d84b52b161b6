{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/auth/__tests__/RouteGuard.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 31831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 31831, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 368, "endOffset": 425, "count": 1}], "isBlockCoverage": true}, {"functionName": "useRouter", "ranges": [{"startOffset": 390, "endOffset": 422, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 940, "endOffset": 14033, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 961, "endOffset": 1022, "count": 10}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1083, "endOffset": 2147, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1331, "endOffset": 1342, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1366, "endOffset": 1376, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2214, "endOffset": 3225, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2394, "endOffset": 2405, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 2429, "endOffset": 2440, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3293, "endOffset": 4375, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3472, "endOffset": 3483, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 3507, "endOffset": 3518, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4437, "endOffset": 5393, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4617, "endOffset": 4628, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 4652, "endOffset": 4663, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5446, "endOffset": 6667, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5484, "endOffset": 5495, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 5738, "endOffset": 5749, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 6729, "endOffset": 7860, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6767, "endOffset": 6777, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7021, "endOffset": 7031, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 7908, "endOffset": 9126, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7946, "endOffset": 7971, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 8214, "endOffset": 8225, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9197, "endOffset": 10386, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 9235, "endOffset": 9246, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 9489, "endOffset": 9500, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10442, "endOffset": 12355, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 10480, "endOffset": 10491, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 10734, "endOffset": 10745, "count": 0}], "isBlockCoverage": false}, {"functionName": "CustomAccessDenied", "ranges": [{"startOffset": 10838, "endOffset": 11128, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12405, "endOffset": 14029, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 12584, "endOffset": 12595, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 12619, "endOffset": 12630, "count": 0}], "isBlockCoverage": false}, {"functionName": "CustomLoading", "ranges": [{"startOffset": 12690, "endOffset": 12969, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1941", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/auth/RouteGuard.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11741, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11741, "count": 1}], "isBlockCoverage": true}, {"functionName": "RouteGuard", "ranges": [{"startOffset": 704, "endOffset": 3306, "count": 10}, {"startOffset": 1531, "endOffset": 2773, "count": 2}, {"startOffset": 2773, "endOffset": 2792, "count": 8}, {"startOffset": 2792, "endOffset": 2811, "count": 0}, {"startOffset": 2813, "endOffset": 2835, "count": 0}, {"startOffset": 2835, "endOffset": 2855, "count": 8}, {"startOffset": 2855, "endOffset": 2890, "count": 0}, {"startOffset": 2892, "endOffset": 2914, "count": 0}, {"startOffset": 2914, "endOffset": 2950, "count": 8}, {"startOffset": 2951, "endOffset": 2969, "count": 8}, {"startOffset": 2970, "endOffset": 3010, "count": 6}, {"startOffset": 3012, "endOffset": 3034, "count": 0}, {"startOffset": 3034, "endOffset": 3305, "count": 8}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 978, "endOffset": 1425, "count": 10}, {"startOffset": 1005, "endOffset": 1012, "count": 2}, {"startOffset": 1012, "endOffset": 1033, "count": 8}, {"startOffset": 1033, "endOffset": 1052, "count": 0}, {"startOffset": 1054, "endOffset": 1118, "count": 0}, {"startOffset": 1118, "endOffset": 1140, "count": 8}, {"startOffset": 1140, "endOffset": 1175, "count": 0}, {"startOffset": 1177, "endOffset": 1245, "count": 0}, {"startOffset": 1245, "endOffset": 1283, "count": 8}, {"startOffset": 1284, "endOffset": 1302, "count": 8}, {"startOffset": 1304, "endOffset": 1421, "count": 6}, {"startOffset": 1355, "endOffset": 1415, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 3405, "endOffset": 3431, "count": 10}], "isBlockCoverage": true}, {"functionName": "<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 3435, "endOffset": 4233, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4330, "endOffset": 4354, "count": 0}], "isBlockCoverage": false}, {"functionName": "with<PERSON><PERSON><PERSON>", "ranges": [{"startOffset": 4358, "endOffset": 5135, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5233, "endOffset": 5258, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1942", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "useAuth", "ranges": [{"startOffset": 846, "endOffset": 3842, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3938, "endOffset": 3961, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1943", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/stores/authStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.persist.name", "ranges": [{"startOffset": 569, "endOffset": 1881, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuth", "ranges": [{"startOffset": 771, "endOffset": 916, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 984, "endOffset": 1077, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuth", "ranges": [{"startOffset": 1141, "endOffset": 1288, "count": 0}], "isBlockCoverage": false}, {"functionName": "setLoading", "ranges": [{"startOffset": 1335, "endOffset": 1445, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeAuth", "ranges": [{"startOffset": 1524, "endOffset": 1874, "count": 1}, {"startOffset": 1585, "endOffset": 1598, "count": 0}, {"startOffset": 1600, "endOffset": 1705, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2014, "endOffset": 2032, "count": 1}], "isBlockCoverage": true}, {"functionName": "partialize", "ranges": [{"startOffset": 2110, "endOffset": 2185, "count": 1}], "isBlockCoverage": true}, {"functionName": "onRehydrateStorage", "ranges": [{"startOffset": 2254, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2459, "endOffset": 2487, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthUser", "ranges": [{"startOffset": 2511, "endOffset": 2552, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2653, "endOffset": 2680, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthToken", "ranges": [{"startOffset": 2705, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2849, "endOffset": 2877, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsAuthenticated", "ranges": [{"startOffset": 2908, "endOffset": 2960, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3068, "endOffset": 3102, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsLoading", "ranges": [{"startOffset": 3127, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3275, "endOffset": 3303, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthToken", "ranges": [{"startOffset": 3328, "endOffset": 3363, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3465, "endOffset": 3493, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthUser", "ranges": [{"startOffset": 3517, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3652, "endOffset": 3679, "count": 0}], "isBlockCoverage": false}, {"functionName": "isUserAuthenticated", "ranges": [{"startOffset": 3711, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3865, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1953", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/api/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "useLogin", "ranges": [{"startOffset": 747, "endOffset": 1601, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1698, "endOffset": 1722, "count": 0}], "isBlockCoverage": false}, {"functionName": "useLogout", "ranges": [{"startOffset": 1726, "endOffset": 2377, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2475, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "useChangePassword", "ranges": [{"startOffset": 2504, "endOffset": 2801, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2907, "endOffset": 2940, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCurrentUser", "ranges": [{"startOffset": 2944, "endOffset": 3406, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3509, "endOffset": 3539, "count": 0}], "isBlockCoverage": false}, {"functionName": "useUpdateProfile", "ranges": [{"startOffset": 3543, "endOffset": 4197, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4302, "endOffset": 4334, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2003", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/api/client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "ApiClient", "ranges": [{"startOffset": 205, "endOffset": 351, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuthToken", "ranges": [{"startOffset": 420, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuthToken", "ranges": [{"startOffset": 520, "endOffset": 567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultHeaders", "ranges": [{"startOffset": 620, "endOffset": 825, "count": 0}], "isBlockCoverage": false}, {"functionName": "request", "ranges": [{"startOffset": 883, "endOffset": 2012, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2044, "endOffset": 2144, "count": 0}], "isBlockCoverage": false}, {"functionName": "post", "ranges": [{"startOffset": 2177, "endOffset": 2351, "count": 0}], "isBlockCoverage": false}, {"functionName": "put", "ranges": [{"startOffset": 2383, "endOffset": 2555, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 2590, "endOffset": 2696, "count": 0}], "isBlockCoverage": false}, {"functionName": "login", "ranges": [{"startOffset": 2729, "endOffset": 2936, "count": 0}], "isBlockCoverage": false}, {"functionName": "logout", "ranges": [{"startOffset": 2939, "endOffset": 3124, "count": 0}], "isBlockCoverage": false}, {"functionName": "changePassword", "ranges": [{"startOffset": 3127, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentUser", "ranges": [{"startOffset": 3362, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateCurrentUser", "ranges": [{"startOffset": 3554, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsers", "ranges": [{"startOffset": 3759, "endOffset": 4457, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsersSummary", "ranges": [{"startOffset": 4460, "endOffset": 4724, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUser", "ranges": [{"startOffset": 4727, "endOffset": 4914, "count": 0}], "isBlockCoverage": false}, {"functionName": "createUser", "ranges": [{"startOffset": 4917, "endOffset": 5110, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 5113, "endOffset": 5315, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteUser", "ranges": [{"startOffset": 5318, "endOffset": 5485, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 5515, "endOffset": 5699, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5799, "endOffset": 5824, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6033, "endOffset": 6058, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2004", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/types/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 470, "endOffset": 494, "count": 0}], "isBlockCoverage": false}, {"functionName": "usersList", "ranges": [{"startOffset": 551, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "user", "ranges": [{"startOffset": 598, "endOffset": 619, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 812, "endOffset": 837, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1220, "endOffset": 1248, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2005", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/auth/tokenManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 443, "endOffset": 4601, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAccessToken", "ranges": [{"startOffset": 639, "endOffset": 828, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessToken", "ranges": [{"startOffset": 890, "endOffset": 1034, "count": 0}], "isBlockCoverage": false}, {"functionName": "setRefreshToken", "ranges": [{"startOffset": 1095, "endOffset": 1229, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRefreshToken", "ranges": [{"startOffset": 1292, "endOffset": 1438, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearTokens", "ranges": [{"startOffset": 1515, "endOffset": 1744, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasAccessToken", "ranges": [{"startOffset": 1800, "endOffset": 1858, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasRefreshToken", "ranges": [{"startOffset": 1915, "endOffset": 1975, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeToken", "ranges": [{"startOffset": 2123, "endOffset": 2571, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTokenExpired", "ranges": [{"startOffset": 2649, "endOffset": 3007, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTokenExpiration", "ranges": [{"startOffset": 3060, "endOffset": 3367, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTokens", "ranges": [{"startOffset": 3427, "endOffset": 3733, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshTokenIfNeeded", "ranges": [{"startOffset": 3861, "endOffset": 4599, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4702, "endOffset": 4730, "count": 0}], "isBlockCoverage": false}]}]}