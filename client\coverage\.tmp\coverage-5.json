{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 1}], "isBlockCoverage": true}, {"functionName": "useRouter", "ranges": [{"startOffset": 349, "endOffset": 365, "count": 0}], "isBlockCoverage": false}, {"functionName": "usePathname", "ranges": [{"startOffset": 382, "endOffset": 391, "count": 0}], "isBlockCoverage": false}, {"functionName": "useSearchParams", "ranges": [{"startOffset": 412, "endOffset": 439, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/auth/__tests__/LoginForm.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 19842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 19842, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 896, "endOffset": 7784, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 979, "endOffset": 1406, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1443, "endOffset": 2032, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2069, "endOffset": 2999, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2559, "endOffset": 2616, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vi_import_2__.waitFor.timeout", "ranges": [{"startOffset": 2653, "endOffset": 2768, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vi_import_2__.waitFor.timeout", "ranges": [{"startOffset": 2823, "endOffset": 2929, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3036, "endOffset": 4064, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3691, "endOffset": 3851, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3888, "endOffset": 4012, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4104, "endOffset": 5223, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4826, "endOffset": 4994, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5031, "endOffset": 5171, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5257, "endOffset": 5865, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5897, "endOffset": 6527, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6582, "endOffset": 7780, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7095, "endOffset": 7152, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vi_import_2__.waitFor.timeout", "ranges": [{"startOffset": 7189, "endOffset": 7304, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 7442, "endOffset": 7507, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vi_import_2__.waitFor.timeout", "ranges": [{"startOffset": 7544, "endOffset": 7665, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1941", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 12160, "count": 1}], "isBlockCoverage": true}, {"functionName": "useAuth", "ranges": [{"startOffset": 846, "endOffset": 3842, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3938, "endOffset": 3961, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1942", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/stores/authStore.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 10467, "count": 1}], "isBlockCoverage": true}, {"functionName": "__vite_ssr_import_0__.create.__vite_ssr_import_1__.persist.name", "ranges": [{"startOffset": 569, "endOffset": 1881, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuth", "ranges": [{"startOffset": 771, "endOffset": 916, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 984, "endOffset": 1077, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuth", "ranges": [{"startOffset": 1141, "endOffset": 1288, "count": 0}], "isBlockCoverage": false}, {"functionName": "setLoading", "ranges": [{"startOffset": 1335, "endOffset": 1445, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeAuth", "ranges": [{"startOffset": 1524, "endOffset": 1874, "count": 1}, {"startOffset": 1585, "endOffset": 1598, "count": 0}, {"startOffset": 1600, "endOffset": 1705, "count": 0}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2014, "endOffset": 2032, "count": 1}], "isBlockCoverage": true}, {"functionName": "partialize", "ranges": [{"startOffset": 2110, "endOffset": 2185, "count": 1}], "isBlockCoverage": true}, {"functionName": "onRehydrateStorage", "ranges": [{"startOffset": 2254, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2260, "endOffset": 2345, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2459, "endOffset": 2487, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthUser", "ranges": [{"startOffset": 2511, "endOffset": 2552, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2653, "endOffset": 2680, "count": 0}], "isBlockCoverage": false}, {"functionName": "useAuthToken", "ranges": [{"startOffset": 2705, "endOffset": 2747, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2849, "endOffset": 2877, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsAuthenticated", "ranges": [{"startOffset": 2908, "endOffset": 2960, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3068, "endOffset": 3102, "count": 0}], "isBlockCoverage": false}, {"functionName": "useIsLoading", "ranges": [{"startOffset": 3127, "endOffset": 3173, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3275, "endOffset": 3303, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthToken", "ranges": [{"startOffset": 3328, "endOffset": 3363, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3465, "endOffset": 3493, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAuthUser", "ranges": [{"startOffset": 3517, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3652, "endOffset": 3679, "count": 0}], "isBlockCoverage": false}, {"functionName": "isUserAuthenticated", "ranges": [{"startOffset": 3711, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3865, "endOffset": 3900, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "1952", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/hooks/api/useAuth.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 11581, "count": 1}], "isBlockCoverage": true}, {"functionName": "useLogin", "ranges": [{"startOffset": 747, "endOffset": 1601, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1698, "endOffset": 1722, "count": 0}], "isBlockCoverage": false}, {"functionName": "useLogout", "ranges": [{"startOffset": 1726, "endOffset": 2377, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2475, "endOffset": 2500, "count": 0}], "isBlockCoverage": false}, {"functionName": "useChangePassword", "ranges": [{"startOffset": 2504, "endOffset": 2801, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2907, "endOffset": 2940, "count": 0}], "isBlockCoverage": false}, {"functionName": "useCurrentUser", "ranges": [{"startOffset": 2944, "endOffset": 3406, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3509, "endOffset": 3539, "count": 0}], "isBlockCoverage": false}, {"functionName": "useUpdateProfile", "ranges": [{"startOffset": 3543, "endOffset": 4197, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4302, "endOffset": 4334, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2002", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/api/client.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 22123, "count": 1}], "isBlockCoverage": true}, {"functionName": "ApiClient", "ranges": [{"startOffset": 205, "endOffset": 351, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAuthToken", "ranges": [{"startOffset": 420, "endOffset": 473, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearAuthToken", "ranges": [{"startOffset": 520, "endOffset": 567, "count": 0}], "isBlockCoverage": false}, {"functionName": "getDefaultHeaders", "ranges": [{"startOffset": 620, "endOffset": 825, "count": 0}], "isBlockCoverage": false}, {"functionName": "request", "ranges": [{"startOffset": 883, "endOffset": 2012, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2044, "endOffset": 2144, "count": 0}], "isBlockCoverage": false}, {"functionName": "post", "ranges": [{"startOffset": 2177, "endOffset": 2351, "count": 0}], "isBlockCoverage": false}, {"functionName": "put", "ranges": [{"startOffset": 2383, "endOffset": 2555, "count": 0}], "isBlockCoverage": false}, {"functionName": "delete", "ranges": [{"startOffset": 2590, "endOffset": 2696, "count": 0}], "isBlockCoverage": false}, {"functionName": "login", "ranges": [{"startOffset": 2729, "endOffset": 2936, "count": 0}], "isBlockCoverage": false}, {"functionName": "logout", "ranges": [{"startOffset": 2939, "endOffset": 3124, "count": 0}], "isBlockCoverage": false}, {"functionName": "changePassword", "ranges": [{"startOffset": 3127, "endOffset": 3339, "count": 0}], "isBlockCoverage": false}, {"functionName": "getCurrentUser", "ranges": [{"startOffset": 3362, "endOffset": 3551, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateCurrentUser", "ranges": [{"startOffset": 3554, "endOffset": 3756, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsers", "ranges": [{"startOffset": 3759, "endOffset": 4457, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUsersSummary", "ranges": [{"startOffset": 4460, "endOffset": 4724, "count": 0}], "isBlockCoverage": false}, {"functionName": "getUser", "ranges": [{"startOffset": 4727, "endOffset": 4914, "count": 0}], "isBlockCoverage": false}, {"functionName": "createUser", "ranges": [{"startOffset": 4917, "endOffset": 5110, "count": 0}], "isBlockCoverage": false}, {"functionName": "updateUser", "ranges": [{"startOffset": 5113, "endOffset": 5315, "count": 0}], "isBlockCoverage": false}, {"functionName": "deleteUser", "ranges": [{"startOffset": 5318, "endOffset": 5485, "count": 0}], "isBlockCoverage": false}, {"functionName": "healthCheck", "ranges": [{"startOffset": 5515, "endOffset": 5699, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 5799, "endOffset": 5824, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 6033, "endOffset": 6058, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2003", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/types/api.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 7779, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 217, "endOffset": 355, "count": 1}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 470, "endOffset": 494, "count": 0}], "isBlockCoverage": false}, {"functionName": "usersList", "ranges": [{"startOffset": 551, "endOffset": 588, "count": 0}], "isBlockCoverage": false}, {"functionName": "user", "ranges": [{"startOffset": 598, "endOffset": 619, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 812, "endOffset": 837, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 1220, "endOffset": 1248, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2004", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/auth/tokenManager.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 14985, "count": 1}], "isBlockCoverage": true}, {"functionName": "<static_initializer>", "ranges": [{"startOffset": 443, "endOffset": 4601, "count": 1}], "isBlockCoverage": true}, {"functionName": "setAccessToken", "ranges": [{"startOffset": 639, "endOffset": 828, "count": 0}], "isBlockCoverage": false}, {"functionName": "getAccessToken", "ranges": [{"startOffset": 890, "endOffset": 1034, "count": 0}], "isBlockCoverage": false}, {"functionName": "setRefreshToken", "ranges": [{"startOffset": 1095, "endOffset": 1229, "count": 0}], "isBlockCoverage": false}, {"functionName": "getRefreshToken", "ranges": [{"startOffset": 1292, "endOffset": 1438, "count": 0}], "isBlockCoverage": false}, {"functionName": "clearTokens", "ranges": [{"startOffset": 1515, "endOffset": 1744, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasAccessToken", "ranges": [{"startOffset": 1800, "endOffset": 1858, "count": 0}], "isBlockCoverage": false}, {"functionName": "hasRefreshToken", "ranges": [{"startOffset": 1915, "endOffset": 1975, "count": 0}], "isBlockCoverage": false}, {"functionName": "decodeToken", "ranges": [{"startOffset": 2123, "endOffset": 2571, "count": 0}], "isBlockCoverage": false}, {"functionName": "isTokenExpired", "ranges": [{"startOffset": 2649, "endOffset": 3007, "count": 0}], "isBlockCoverage": false}, {"functionName": "getTokenExpiration", "ranges": [{"startOffset": 3060, "endOffset": 3367, "count": 0}], "isBlockCoverage": false}, {"functionName": "initializeTokens", "ranges": [{"startOffset": 3427, "endOffset": 3733, "count": 0}], "isBlockCoverage": false}, {"functionName": "refreshTokenIfNeeded", "ranges": [{"startOffset": 3861, "endOffset": 4599, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4702, "endOffset": 4730, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2005", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 663, "endOffset": 801, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 852, "endOffset": 1024, "count": 7}], "isBlockCoverage": true}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 1026, "endOffset": 1602, "count": 7}], "isBlockCoverage": true}, {"functionName": "Wrapper", "ranges": [{"startOffset": 1156, "endOffset": 1487, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1710, "endOffset": 1745, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2108, "endOffset": 2132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2386, "endOffset": 2415, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2646, "endOffset": 2679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2905, "endOffset": 2933, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetch", "ranges": [{"startOffset": 2955, "endOffset": 3122, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3221, "endOffset": 3246, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetchError", "ranges": [{"startOffset": 3273, "endOffset": 3410, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3514, "endOffset": 3544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3937, "endOffset": 3978, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4231, "endOffset": 4276, "count": 4}], "isBlockCoverage": true}]}, {"scriptId": "2149", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/auth/LoginForm.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 20262, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 20262, "count": 1}], "isBlockCoverage": true}, {"functionName": "LoginForm", "ranges": [{"startOffset": 697, "endOffset": 9324, "count": 61}, {"startOffset": 2742, "endOffset": 2760, "count": 2}, {"startOffset": 2761, "endOffset": 2780, "count": 59}, {"startOffset": 3295, "endOffset": 3635, "count": 2}, {"startOffset": 4541, "endOffset": 4559, "count": 7}, {"startOffset": 4560, "endOffset": 4579, "count": 54}, {"startOffset": 5086, "endOffset": 5427, "count": 7}, {"startOffset": 5627, "endOffset": 7115, "count": 1}, {"startOffset": 6350, "endOffset": 6383, "count": 0}, {"startOffset": 7349, "endOffset": 8914, "count": 1}, {"startOffset": 8915, "endOffset": 8926, "count": 60}], "isBlockCoverage": true}, {"functionName": "handleInputChange", "ranges": [{"startOffset": 1027, "endOffset": 1256, "count": 50}, {"startOffset": 1169, "endOffset": 1252, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1090, "endOffset": 1144, "count": 50}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1187, "endOffset": 1244, "count": 1}], "isBlockCoverage": true}, {"functionName": "handleSubmit", "ranges": [{"startOffset": 1281, "endOffset": 1886, "count": 4}, {"startOffset": 1381, "endOffset": 1448, "count": 2}, {"startOffset": 1477, "endOffset": 1651, "count": 2}, {"startOffset": 1575, "endOffset": 1651, "count": 1}, {"startOffset": 1728, "endOffset": 1749, "count": 3}, {"startOffset": 1749, "endOffset": 1808, "count": 1}, {"startOffset": 1816, "endOffset": 1882, "count": 0}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 9422, "endOffset": 9447, "count": 7}], "isBlockCoverage": true}]}, {"scriptId": "2150", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2095, "endOffset": 2585, "count": 61}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2715, "endOffset": 2737, "count": 61}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2843, "endOffset": 2873, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2153", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1282, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 61}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 61}], "isBlockCoverage": true}]}]}