{"result": [{"scriptId": "1315", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/setup.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 4592, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 327, "endOffset": 442, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 466, "endOffset": 633, "count": 0}], "isBlockCoverage": false}, {"functionName": "", "ranges": [{"startOffset": 1423, "endOffset": 1483, "count": 7}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1496, "endOffset": 1505, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1517, "endOffset": 1526, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1936", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/ui/__tests__/button.test.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 13281, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 13281, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 774, "endOffset": 6783, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 839, "endOffset": 1441, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 1506, "endOffset": 2950, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 3012, "endOffset": 4394, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 4448, "endOffset": 5084, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5156, "endOffset": 5721, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 5779, "endOffset": 6289, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 6345, "endOffset": 6779, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "1941", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/test/utils.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 9863, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 663, "endOffset": 801, "count": 1}], "isBlockCoverage": true}, {"functionName": "createTestQueryClient", "ranges": [{"startOffset": 852, "endOffset": 1024, "count": 7}], "isBlockCoverage": true}, {"functionName": "renderWithProviders", "ranges": [{"startOffset": 1026, "endOffset": 1602, "count": 7}], "isBlockCoverage": true}, {"functionName": "Wrapper", "ranges": [{"startOffset": 1156, "endOffset": 1487, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 1710, "endOffset": 1745, "count": 7}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2108, "endOffset": 2132, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2386, "endOffset": 2415, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2646, "endOffset": 2679, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 2905, "endOffset": 2933, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetch", "ranges": [{"startOffset": 2955, "endOffset": 3122, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3221, "endOffset": 3246, "count": 0}], "isBlockCoverage": false}, {"functionName": "mockFetchError", "ranges": [{"startOffset": 3273, "endOffset": 3410, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3514, "endOffset": 3544, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 3937, "endOffset": 3978, "count": 0}], "isBlockCoverage": false}, {"functionName": "get", "ranges": [{"startOffset": 4231, "endOffset": 4276, "count": 1}], "isBlockCoverage": true}]}, {"scriptId": "2134", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/components/ui/button.tsx", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 6912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 6912, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 2095, "endOffset": 2585, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2715, "endOffset": 2737, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 2843, "endOffset": 2873, "count": 0}], "isBlockCoverage": false}]}, {"scriptId": "2137", "url": "file:///D:/Projects/ultimate-electrical-designer/client/src/lib/utils.ts", "functions": [{"functionName": "", "ranges": [{"startOffset": 0, "endOffset": 1282, "count": 1}], "isBlockCoverage": true}, {"functionName": "", "ranges": [{"startOffset": 13, "endOffset": 1282, "count": 1}], "isBlockCoverage": true}, {"functionName": "cn", "ranges": [{"startOffset": 448, "endOffset": 550, "count": 11}], "isBlockCoverage": true}, {"functionName": "get", "ranges": [{"startOffset": 641, "endOffset": 659, "count": 11}], "isBlockCoverage": true}]}]}